# 「沉淀」知识卡片生成器 UI 设计需求文档

**项目名称：** 沉淀 (Precipitation)  
**版本：** 4.0 MVP  
**文档类型：** UI/UX 设计需求  
**日期：** 2025年1月  

---

## 1. 项目概述

### 1.1 产品定位
智能知识内化工具，通过AI技术将网页内容和文本自动转换为结构化知识卡片，提供沉浸式的阅读和学习体验。

### 1.2 目标用户
- **主要用户：** 知识工作者、研究员、产品经理
- **年龄范围：** 25-45岁
- **技术水平：** 中高等，熟悉现代Web应用
- **使用场景：** 日常工作中的信息处理、学习笔记整理

---

## 2. 设计原则与风格指南

### 2.1 设计原则
- **简洁专注：** 减少视觉干扰，突出核心功能
- **沉浸体验：** 提供专注的阅读和思考环境
- **高效交互：** 最少步骤完成核心任务
- **智能响应：** 适配各种屏幕尺寸和使用场景

### 2.2 视觉风格
- **整体调性：** 现代、简洁、专业、宁静
- **色彩方案：** 
  - 主色调：深蓝色系 (#1e40af 或类似)
  - 辅助色：浅灰色系 (#f8fafc, #e2e8f0)
  - 强调色：橙色系 (#f59e0b) 用于重要按钮
  - 文字色：深灰色 (#1f2937, #6b7280)
- **字体层级：** 清晰的标题、正文、辅助文字层级
- **圆角：** 统一使用 8px 圆角
- **阴影：** 轻量级阴影增强层次感

### 2.3 响应式要求
- **桌面端：** 1440px+ (主要使用场景)
- **平板端：** 768px-1439px
- **移动端：** 320px-767px (简化版本)

---

## 3. 界面清单与优先级

| 界面名称 | 优先级 | 复杂度 | 说明 |
|---------|--------|--------|------|
| 主工作台 | P0 | 高 | 核心功能界面 |
| 欢迎引导页 | P0 | 中 | 首次使用引导 |
| 知识库列表 | P1 | 中 | 历史记录管理 |
| AI对话窗口 | P0 | 中 | 嵌入式聊天界面 |
| 设置/配置页 | P2 | 低 | FastGPT配置等 |
| 错误状态页 | P1 | 低 | 各种错误提示 |

---

## 4. 详细界面设计需求

### 4.1 主工作台 (核心界面)

#### 4.1.1 整体布局
- **布局结构：** 两栏式布局 (6:4 比例)
- **左栏：** 原文展示区域
- **右栏：** AI笔记展示区域 + 底部AI问答
- **顶部：** 导航栏 (Logo + 输入框 + 功能按钮)

#### 4.1.2 顶部导航栏
**组件内容：**
- 左侧：品牌Logo "沉淀" + 副标题
- 中间：URL/文本输入框 (宽度自适应)
- 右侧：知识库按钮、设置按钮

**设计要求：**
- 高度：64px
- 背景：白色，底部细线分割
- 输入框：圆角设计，有placeholder提示
- 按钮：图标+文字组合，hover状态明显

**输入框规格：**
- 占位符文字："粘贴网页链接或直接输入文本..."
- 右侧："生成笔记"按钮 (主色调背景)
- 支持拖拽文件的视觉提示

#### 4.1.3 左栏 - 原文展示区
**功能区域：**
- 顶部：文章标题 + 来源信息 + 操作按钮
- 主体：原文内容渲染区
- 底部：滚动进度指示

**设计细节：**
- 内容区padding: 32px
- 文章标题：24px 粗体
- 正文：16px 行高1.6
- 代码/引用块：特殊样式
- 滚动条：自定义样式
- 文本选择：自定义高亮色

**交互需求：**
- 平滑滚动到指定位置 (来自右栏点击)
- 文本选择可触发快速问答
- 加载状态的骨架屏

#### 4.1.4 右栏 - AI笔记区域
**区域划分：**
- 上部：AI笔记展示 (占70%)
- 下部：AI问答聊天窗口 (占30%)

**AI笔记区设计：**
- 标题："AI 笔记" + 保存按钮
- 内容：Markdown渲染，清晰的层级结构
- 大纲点击可跳转到左栏对应位置
- 生成中的流式显示动画

**AI问答区设计：**
- 顶部：推荐问题卡片 (3-4个)
- 中间：对话历史
- 底部：输入框 + 发送按钮
- 消息气泡：用户(右对齐)、AI(左对齐)

#### 4.1.5 状态管理
**加载状态：**
- 输入处理：顶部进度条
- AI生成：右栏流式输出动画
- 网页抓取：左栏骨架屏

**错误状态：**
- 网络错误：重试按钮
- 内容解析失败：手动输入选项
- AI服务异常：降级提示

### 4.2 欢迎引导页

#### 4.2.1 页面结构
- **主标题：** "沉淀" + 产品slogan
- **功能介绍：** 3个核心特性卡片
- **快速开始：** 示例URL + 体验按钮

#### 4.2.2 设计要求
- 居中布局，最大宽度1200px
- 渐变背景或微妙纹理
- 功能卡片：图标+标题+描述
- 行动召唤按钮明显

### 4.3 知识库列表页

#### 4.3.1 页面布局
- **顶部：** 面包屑导航 + 同步按钮
- **主体：** 卡片列表 (网格或列表视图)
- **侧边：** 筛选和搜索

#### 4.3.2 卡片设计
- 标题 + 摘要 + 创建时间
- 来源类型图标 (URL/文本)
- 操作按钮：查看、删除
- hover状态增强

### 4.4 移动端适配

#### 4.4.1 布局调整
- **单栏布局：** 标签页切换原文/笔记
- **底部导航：** 主要功能按钮
- **简化交互：** 减少hover效果

---

## 5. 交互规范

### 5.1 核心用户流程
1. **输入内容** → 2. **查看生成过程** → 3. **阅读对比** → 4. **保存到知识库**

### 5.2 微交互设计
- **按钮点击：** 轻微缩放 + 颜色变化
- **卡片hover：** 轻微上浮 + 阴影增强
- **滚动联动：** 平滑缓动，150ms过渡
- **加载动画：** 简洁的进度指示

### 5.3 手势支持 (移动端)
- 左右滑动切换原文/笔记
- 下拉刷新
- 长按触发快捷菜单

---

## 6. 技术实现要求

### 6.1 前端框架
- React + Next.js
- Tailwind CSS 样式
- shadcn/ui 组件库

### 6.2 动画库
- Framer Motion (页面过渡)
- 自定义CSS动画 (微交互)

### 6.3 图标系统
- 统一的图标风格 (线性图标推荐)
- SVG格式，支持多色
- 常用尺寸：16px, 20px, 24px

---

## 7. 设计交付清单

### 7.1 设计稿
- [ ] 主工作台 (桌面端)
- [ ] 主工作台 (平板/移动端)
- [ ] 欢迎页面
- [ ] 知识库列表页
- [ ] 各种状态页面 (加载、错误、空状态)

### 7.2 设计规范
- [ ] 颜色规范 (色板)
- [ ] 字体规范 (字号、行高、字重)
- [ ] 间距规范 (padding、margin)
- [ ] 组件规范 (按钮、卡片、表单等)

### 7.3 交互原型
- [ ] 核心流程交互原型
- [ ] 关键动画效果演示
- [ ] 响应式适配演示

### 7.4 切图资源
- [ ] 图标SVG文件
- [ ] 示例图片
- [ ] 背景纹理/渐变

---

## 8. 验收标准

### 8.1 视觉要求
- 界面美观，符合现代设计趋势
- 颜色搭配和谐，层次分明
- 字体清晰易读，合理的字号层级

### 8.2 交互要求
- 操作直观，符合用户心智模型
- 反馈及时，状态变化明确
- 响应式适配良好

### 8.3 技术要求
- 设计稿可直接转换为代码
- 切图资源完整，命名规范
- 兼容主流浏览器

---

**联系方式：** [设计师对接联系方式]  
**项目周期：** [预期设计周期]  
**修订记录：** v1.0 - 初版需求文档 