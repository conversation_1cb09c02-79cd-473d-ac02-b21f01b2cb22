{"name": "knowledge-cards", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "electron": "nextron", "electron:build": "nextron build", "electron:dev": "nextron"}, "dependencies": {"@mozilla/readability": "^0.6.0", "@prisma/client": "^6.9.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tailwindcss/typography": "^0.5.16", "@types/jsdom": "^21.1.7", "axios": "^1.9.0", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "http-proxy-middleware": "^3.0.5", "jsdom": "^26.1.0", "lucide-react": "^0.514.0", "next": "15.3.3", "node-html-parser": "^7.0.1", "openai": "^5.3.0", "playwright": "^1.53.0", "prisma": "^6.9.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-iframe": "^1.8.5", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "electron": "^36.4.0", "electron-builder": "^26.0.12", "eslint": "^9", "eslint-config-next": "15.3.3", "nextron": "^9.5.0", "typescript": "^5"}, "main": "app/electron.js", "homepage": "./", "build": {"appId": "com.precipitation.knowledge-cards", "productName": "沉淀", "directories": {"output": "dist"}, "files": ["app/**/*", "electron-src/**/*"], "mac": {"category": "public.app-category.productivity", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}}}