'use client'

import React, { useState, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import EnhancedStructuredNotes from '@/components/ui/EnhancedStructuredNotes'
import PositionableContent from '@/components/ui/PositionableContent'
import { TextSegment, NoteReference } from '@/lib/store'
import { Sparkles, FileText, Globe, Loader2 } from 'lucide-react'

/**
 * 增强AI笔记生成系统演示页面
 */
export default function EnhancedNotesDemo() {
  const [input, setInput] = useState('')
  const [inputType, setInputType] = useState<'text' | 'url'>('text')
  const [isProcessing, setIsProcessing] = useState(false)
  const [result, setResult] = useState<{
    title: string
    content: string
    textSegments: TextSegment[]
    aiNote: string
    aiNoteWithMarkup: string
    noteReferences: NoteReference[]
    hasLocationMarks: boolean
    statistics: any
  } | null>(null)
  const [highlightedSegments, setHighlightedSegments] = useState<string[]>([])
  const [isNotesExpanded, setIsNotesExpanded] = useState(true)

  // 处理内容提交
  const handleSubmit = useCallback(async () => {
    if (!input.trim()) return

    setIsProcessing(true)
    try {
      const response = await fetch('/api/enhanced-process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          input: input.trim(),
          type: inputType
        })
      })

      if (!response.ok) {
        throw new Error('处理失败')
      }

      const data = await response.json()
      setResult(data)
      setHighlightedSegments([])
    } catch (error) {
      console.error('处理失败:', error)
      alert('处理失败，请稍后重试')
    } finally {
      setIsProcessing(false)
    }
  }, [input, inputType])

  // 处理片段高亮
  const handleSegmentHighlight = useCallback((segmentIds: string[]) => {
    setHighlightedSegments(segmentIds)
  }, [])

  // 处理滚动到片段
  const handleScrollToSegment = useCallback((segmentId: string) => {
    // 这里可以实现滚动到特定片段的逻辑
    console.log('滚动到片段:', segmentId)
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            增强AI笔记生成系统
          </h1>
          <p className="text-gray-600">
            支持隐藏定位标记的智能笔记生成，实现笔记与原文的精确双向定位
          </p>
        </div>

        {/* 输入区域 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setInputType('text')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  inputType === 'text'
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <FileText className="w-4 h-4" />
                <span>文本内容</span>
              </button>
              <button
                onClick={() => setInputType('url')}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  inputType === 'url'
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Globe className="w-4 h-4" />
                <span>网页链接</span>
              </button>
            </div>
          </div>

          <div className="space-y-4">
            {inputType === 'text' ? (
              <Textarea
                placeholder="请输入要分析的文本内容..."
                value={input}
                onChange={(e) => setInput(e.target.value)}
                rows={6}
                className="w-full"
              />
            ) : (
              <Input
                placeholder="请输入网页链接，如：https://example.com"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                className="w-full"
              />
            )}

            <Button
              onClick={handleSubmit}
              disabled={!input.trim() || isProcessing}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  正在处理...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  生成智能笔记
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 结果展示区域 */}
        {result && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 原文内容 */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                原文内容
              </h2>
              <PositionableContent
                content={result.content}
                textSegments={result.textSegments}
                noteReferences={result.noteReferences}
                highlightedSegments={highlightedSegments}
                onSegmentHighlight={handleSegmentHighlight}
                onScrollToSegment={handleScrollToSegment}
              />
            </div>

            {/* 智能笔记 */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                智能笔记
              </h2>
              <div className="relative h-96">
                <EnhancedStructuredNotes
                  content={result.aiNote}
                  isAnalyzing={false}
                  isExpanded={isNotesExpanded}
                  height={90}
                  onToggleExpanded={() => setIsNotesExpanded(!isNotesExpanded)}
                  onManualExpand={() => setIsNotesExpanded(true)}
                  onManualCollapse={() => setIsNotesExpanded(false)}
                  fullscreen={true}
                  noteReferences={result.noteReferences}
                  textSegments={result.textSegments}
                  onSegmentHighlight={handleSegmentHighlight}
                  onScrollToSegment={handleScrollToSegment}
                />
              </div>
            </div>
          </div>
        )}

        {/* 统计信息 */}
        {result && (
          <div className="bg-white rounded-lg shadow-md p-6 mt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              处理统计
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {result.textSegments.length}
                </div>
                <div className="text-sm text-gray-600">文本片段</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {result.noteReferences.length}
                </div>
                <div className="text-sm text-gray-600">映射关系</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round(result.statistics.averageConfidence * 100)}%
                </div>
                <div className="text-sm text-gray-600">平均置信度</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {result.hasLocationMarks ? '是' : '否'}
                </div>
                <div className="text-sm text-gray-600">包含定位标记</div>
              </div>
            </div>

            {/* 调试信息 */}
            {result.aiNoteWithMarkup && (
              <details className="mt-4">
                <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                  查看原始AI输出（包含定位标记）
                </summary>
                <pre className="mt-2 p-4 bg-gray-100 rounded-lg text-xs overflow-x-auto">
                  {result.aiNoteWithMarkup}
                </pre>
              </details>
            )}
          </div>
        )}

        {/* 使用说明 */}
        <div className="bg-blue-50 rounded-lg p-6 mt-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">
            使用说明
          </h3>
          <ul className="space-y-2 text-blue-800 text-sm">
            <li>• 输入文本内容或网页链接，系统会自动生成带定位功能的智能笔记</li>
            <li>• 点击笔记中的内容可以高亮显示原文中的对应位置</li>
            <li>• 点击原文中的片段可以查看相关的笔记内容</li>
            <li>• 悬停在可定位的内容上会显示详细的关联信息</li>
            <li>• AI生成的定位标记在用户界面中是隐藏的，只用于内部定位</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
