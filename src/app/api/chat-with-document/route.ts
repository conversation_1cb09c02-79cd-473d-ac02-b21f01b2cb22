import { NextRequest, NextResponse } from 'next/server'
import { askQuestionWithContext } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const { 
      message, 
      context, 
      chatHistory = [] 
    } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    if (!context || (!context.originalContent && !context.aiNote)) {
      return NextResponse.json(
        { error: 'Document context is required' },
        { status: 400 }
      )
    }

    // 构建文档上下文
    const documentContext = `
原文内容：
${context.originalContent || ''}

AI笔记：
${context.aiNote || ''}
    `.trim()

    // 沉淀模式：基于文档内容和对话历史进行问答
    const answer = await askQuestionWithContext(message, documentContext, chatHistory)

    return NextResponse.json({ answer })
    
  } catch (error) {
    console.error('Chat with document API error:', error)
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    )
  }
} 