'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Save, Check, Copy, Cloud, CloudOff, RefreshCw } from 'lucide-react'

interface KnowledgeCard {
  title: string
  content: string
}

interface KnowledgeCardsProps {
  cards: KnowledgeCard[]
  loading: boolean
  onSaveCard: (card: KnowledgeCard) => Promise<void>
}

function CardSkeleton() {
  return (
    <Card className="animate-pulse">
      <CardHeader>
        <div className="skeleton h-5 w-3/4"></div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="skeleton h-4 w-full"></div>
        <div className="skeleton h-4 w-full"></div>
        <div className="skeleton h-4 w-2/3"></div>
        <div className="mt-4">
          <div className="skeleton h-8 w-20"></div>
        </div>
      </CardContent>
    </Card>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      {[...Array(3)].map((_, i) => (
        <CardSkeleton key={i} />
      ))}
    </div>
  )
}

interface SaveableCardProps {
  card: KnowledgeCard
  onSave: (card: KnowledgeCard) => Promise<void>
}

function SaveableCard({ card, onSave }: SaveableCardProps) {
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [copied, setCopied] = useState(false)
  const [syncStatus, setSyncStatus] = useState<'pending' | 'syncing' | 'synced' | 'failed'>('pending')

  const handleSave = async () => {
    setSaving(true)
    setSyncStatus('syncing')
    try {
      await onSave(card)
      setSaved(true)
      // 模拟同步过程 - 实际同步状态会从后端返回
      setTimeout(() => {
        setSyncStatus('synced')
      }, 2000)
      // 3秒后重置保存状态
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error('保存失败:', error)
      setSyncStatus('failed')
    } finally {
      setSaving(false)
    }
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(`${card.title}\n\n${card.content}`)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  const renderSyncStatus = () => {
    switch (syncStatus) {
      case 'syncing':
        return (
          <div className="flex items-center space-x-1 text-xs text-blue-600">
            <RefreshCw className="h-3 w-3 animate-spin" />
            <span>同步中</span>
          </div>
        )
      case 'synced':
        return (
          <div className="flex items-center space-x-1 text-xs text-green-600">
            <Cloud className="h-3 w-3" />
            <span>已同步</span>
          </div>
        )
      case 'failed':
        return (
          <div className="flex items-center space-x-1 text-xs text-red-600">
            <CloudOff className="h-3 w-3" />
            <span>同步失败</span>
          </div>
        )
      default:
        return null
    }
  }

  return (
    <Card className="group hover:shadow-large hover:-translate-y-1 transition-all duration-300 border-border/30 hover:border-primary/20 bg-gradient-to-br from-card via-card to-card/95">
      <CardHeader className="pb-3 relative">
        <div className="flex items-start justify-between">
          <CardTitle className="text-base font-semibold leading-tight text-foreground group-hover:text-primary transition-colors duration-200">
            {card.title}
          </CardTitle>
          <Button
            variant="ghost"
            size="icon-sm"
            onClick={handleCopy}
            className={`opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-primary/10 ${
              copied ? 'opacity-100 bg-green-100 text-green-600' : ''
            }`}
            title={copied ? '已复制!' : '复制内容'}
          >
            <Copy className={`h-4 w-4 transition-transform duration-200 ${copied ? 'scale-110' : ''}`} />
          </Button>
        </div>
        {/* 装饰性渐变线 */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </CardHeader>
      <CardContent className="pt-0 relative">
        <p className="text-sm text-muted-foreground leading-relaxed mb-6 group-hover:text-foreground/80 transition-colors duration-200">
          {card.content}
        </p>
        <div className="flex items-center justify-between">
          <Button
            onClick={handleSave}
            disabled={saving || saved}
            size="sm"
            variant={saved ? "default" : "outline"}
            className={`transition-all duration-300 ${
              saved
                ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-glow border-0'
                : 'hover:border-primary/50 hover:bg-primary/5'
            }`}
          >
            {saving ? (
              <>
                <div className="mr-2 w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                保存中...
              </>
            ) : saved ? (
              <>
                <Check className="mr-2 h-4 w-4" />
                已保存
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                保存
              </>
            )}
          </Button>

          {/* 卡片标签和同步状态 */}
          <div className="flex items-center space-x-2">
            {renderSyncStatus()}
            <div className="px-2 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              知识卡片
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function KnowledgeCards({ cards, loading, onSaveCard }: KnowledgeCardsProps) {
  if (loading) {
    return <LoadingSkeleton />
  }

  if (cards.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 space-y-6">
        <div className="relative">
          <div className="w-20 h-20 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-2xl flex items-center justify-center">
            <div className="text-3xl">💡</div>
          </div>
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
            <span className="text-xs">✨</span>
          </div>
        </div>
        <div className="text-center space-y-3">
          <p className="font-semibold text-foreground text-lg">暂无知识卡片</p>
          <p className="text-sm text-muted-foreground max-w-xs leading-relaxed">
            处理内容后将自动生成结构化的知识卡片，让复杂信息变得简单易懂
          </p>
          <div className="flex items-center justify-center space-x-2 pt-2">
            <div className="w-2 h-2 bg-primary/40 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-primary/60 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2 h-2 bg-primary/80 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {cards.map((card, index) => (
        <SaveableCard
          key={index}
          card={card}
          onSave={onSaveCard}
        />
      ))}
    </div>
  )
} 