'use client'

import React, { memo, useRef, useEffect, useState, useCallback } from 'react'
import { TextSegment, NoteReference } from '@/lib/store'
import { MapPin, Eye, EyeOff } from 'lucide-react'
import { createBidirectionalNavigator, NavigationCallbacks } from '@/lib/bidirectionalNavigator'

interface PositionableContentProps {
  content: string
  textSegments: TextSegment[]
  noteReferences: NoteReference[]
  highlightedSegments?: string[]
  onSegmentClick?: (segmentId: string) => void
  onSegmentHover?: (segmentId: string | null) => void
  onNoteHighlight?: (noteIds: string[]) => void
  className?: string
}

/**
 * 支持定位和高亮的内容显示组件
 */
const PositionableContent = memo<PositionableContentProps>(({
  content,
  textSegments,
  noteReferences,
  highlightedSegments = [],
  onSegmentClick,
  onSegmentHover,
  onNoteHighlight,
  className = ''
}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const [showPositions, setShowPositions] = useState(false)
  const [hoveredSegment, setHoveredSegment] = useState<string | null>(null)
  const [tooltip, setTooltip] = useState<{ content: string, x: number, y: number } | null>(null)

  // 创建双向导航器
  const navigator = React.useMemo(() => {
    const callbacks: NavigationCallbacks = {
      onSegmentHighlight: (segmentIds) => {
        // 这里处理片段高亮，但不需要额外操作，因为已经通过props处理
      },
      onNoteHighlight: (noteIds) => {
        onNoteHighlight?.(noteIds)
      },
      onShowTooltip: (content, position) => {
        setTooltip({ content, x: position.x, y: position.y })
      },
      onHideTooltip: () => {
        setTooltip(null)
      }
    }

    const nav = createBidirectionalNavigator(callbacks)
    nav.initialize(noteReferences, textSegments)
    return nav
  }, [noteReferences, textSegments, onNoteHighlight])

  // 创建片段ID到映射关系的索引（保持向后兼容）
  const segmentToNoteMap = React.useMemo(() => {
    const map = new Map<string, NoteReference[]>()

    for (const noteRef of noteReferences) {
      for (const segmentId of noteRef.sourceSegments) {
        if (!map.has(segmentId)) {
          map.set(segmentId, [])
        }
        map.get(segmentId)!.push(noteRef)
      }
    }

    return map
  }, [noteReferences])

  // 处理片段点击
  const handleSegmentClick = useCallback((segmentId: string, event?: React.MouseEvent) => {
    onSegmentClick?.(segmentId)
    navigator.handleSegmentClick(segmentId, event?.nativeEvent)
  }, [onSegmentClick, navigator])

  // 处理片段悬停
  const handleSegmentHover = useCallback((segmentId: string | null, event?: React.MouseEvent) => {
    setHoveredSegment(segmentId)
    onSegmentHover?.(segmentId)

    if (segmentId && event) {
      navigator.handleSegmentHover(segmentId, event.nativeEvent)
    } else {
      navigator.clearHover()
    }
  }, [onSegmentHover, navigator])

  // 渲染带高亮的内容
  const renderHighlightedContent = useCallback(() => {
    if (!textSegments || textSegments.length === 0) {
      return <div className="prose prose-sm max-w-none">{content}</div>
    }

    // 按位置排序片段
    const sortedSegments = [...textSegments].sort((a, b) => a.startOffset - b.startOffset)
    const elements: React.ReactNode[] = []
    let lastOffset = 0

    for (let i = 0; i < sortedSegments.length; i++) {
      const segment = sortedSegments[i]
      
      // 添加片段前的内容
      if (segment.startOffset > lastOffset) {
        const beforeText = content.substring(lastOffset, segment.startOffset)
        if (beforeText.trim()) {
          elements.push(
            <span key={`before-${i}`} className="text-gray-700">
              {beforeText}
            </span>
          )
        }
      }

      // 确定片段样式
      const isHighlighted = highlightedSegments.includes(segment.id)
      const isHovered = hoveredSegment === segment.id
      const hasNoteMapping = segmentToNoteMap.has(segment.id)
      const relatedNotes = segmentToNoteMap.get(segment.id) || []

      let segmentClassName = 'relative inline-block transition-all duration-200 '
      
      if (hasNoteMapping) {
        segmentClassName += 'cursor-pointer border-b-2 border-blue-200 hover:border-blue-400 hover:bg-blue-50 '
      }
      
      if (isHighlighted) {
        segmentClassName += 'bg-yellow-200 border-yellow-400 '
      }
      
      if (isHovered) {
        segmentClassName += 'bg-blue-100 border-blue-500 '
      }

      // 渲染片段
      elements.push(
        <span
          key={segment.id}
          className={segmentClassName}
          onClick={(e) => hasNoteMapping && handleSegmentClick(segment.id, e)}
          onMouseEnter={(e) => hasNoteMapping && handleSegmentHover(segment.id, e)}
          onMouseLeave={() => handleSegmentHover(null)}
          title={hasNoteMapping ? `关联 ${relatedNotes.length} 条笔记` : undefined}
          data-segment-id={segment.id}
        >
          {segment.content}
          
          {/* 位置指示器 */}
          {showPositions && hasNoteMapping && (
            <span className="absolute -top-6 left-0 text-xs bg-blue-600 text-white px-1 py-0.5 rounded opacity-75">
              <MapPin className="w-3 h-3 inline mr-1" />
              {relatedNotes.length}
            </span>
          )}
          
          {/* 悬停提示 */}
          {isHovered && relatedNotes.length > 0 && (
            <div className="absolute z-50 bottom-full left-0 mb-2 p-2 bg-gray-800 text-white text-xs rounded shadow-lg max-w-xs">
              <div className="font-semibold mb-1">相关笔记:</div>
              {relatedNotes.slice(0, 2).map((note, idx) => (
                <div key={idx} className="truncate">
                  • {note.noteContent.substring(0, 50)}...
                </div>
              ))}
              {relatedNotes.length > 2 && (
                <div className="text-gray-300">还有 {relatedNotes.length - 2} 条...</div>
              )}
            </div>
          )}
        </span>
      )

      lastOffset = segment.endOffset
    }

    // 添加最后的内容
    if (lastOffset < content.length) {
      const remainingText = content.substring(lastOffset)
      if (remainingText.trim()) {
        elements.push(
          <span key="remaining" className="text-gray-700">
            {remainingText}
          </span>
        )
      }
    }

    return (
      <div className="prose prose-sm max-w-none leading-relaxed">
        {elements}
      </div>
    )
  }, [content, textSegments, highlightedSegments, hoveredSegment, segmentToNoteMap, showPositions, handleSegmentClick, handleSegmentHover])

  // 滚动到指定片段
  const scrollToSegment = useCallback((segmentId: string) => {
    if (!contentRef.current) return

    const segmentElement = contentRef.current.querySelector(`[data-segment-id="${segmentId}"]`)
    if (segmentElement) {
      segmentElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      })
      
      // 临时高亮
      segmentElement.classList.add('bg-yellow-300')
      setTimeout(() => {
        segmentElement.classList.remove('bg-yellow-300')
      }, 2000)
    }
  }, [])

  // 暴露滚动方法给父组件
  useEffect(() => {
    if (contentRef.current) {
      (contentRef.current as any).scrollToSegment = scrollToSegment
    }
  }, [scrollToSegment])

  return (
    <>
      <div className={`relative ${className}`}>
        {/* 控制栏 */}
        <div className="flex items-center justify-between mb-4 p-2 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              {textSegments.length} 个文本片段
            </span>
            <span className="text-sm text-gray-600">
              {noteReferences.length} 个映射关系
            </span>
          </div>

          <button
            onClick={() => setShowPositions(!showPositions)}
            className="flex items-center space-x-1 px-3 py-1 text-sm bg-white border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
          >
            {showPositions ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            <span>{showPositions ? '隐藏' : '显示'}位置</span>
          </button>
        </div>

        {/* 内容区域 */}
        <div
          ref={contentRef}
          className="bg-white rounded-lg p-6 border border-gray-200 max-h-96 overflow-y-auto"
        >
          {renderHighlightedContent()}
        </div>

        {/* 统计信息 */}
        <div className="mt-2 text-xs text-gray-500 flex justify-between">
          <span>
            高亮片段: {highlightedSegments.length}
          </span>
          <span>
            可定位片段: {Array.from(segmentToNoteMap.keys()).length}
          </span>
        </div>
      </div>

      {/* 悬停提示 */}
      {tooltip && (
        <div
          className="fixed z-[100] p-3 bg-gray-800 text-white text-sm rounded-lg shadow-xl max-w-xs pointer-events-none"
          style={{
            left: tooltip.x + 10,
            top: tooltip.y - 10,
            transform: 'translateY(-100%)'
          }}
          dangerouslySetInnerHTML={{ __html: tooltip.content }}
        />
      )}
    </>
  )
})

PositionableContent.displayName = 'PositionableContent'

export default PositionableContent
