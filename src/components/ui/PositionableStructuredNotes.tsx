'use client'

import React, { memo, useMemo, useCallback, useRef, useEffect, useState } from 'react'
import { Sparkles, ChevronUp, ChevronDown, MapPin, Target } from 'lucide-react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import { useSmartScrollFollow } from '@/hooks/useSmartScrollFollow'
import { NoteReference, TextSegment } from '@/lib/store'

interface PositionableStructuredNotesProps {
  content: string
  streamingContent?: string
  isAnalyzing?: boolean
  isExpanded: boolean
  height: number
  onToggleExpanded: () => void
  onManualExpand: () => void
  onManualCollapse: () => void
  className?: string
  fullscreen?: boolean
  
  // 新增：定位功能相关属性
  noteReferences?: NoteReference[]
  textSegments?: TextSegment[]
  onNoteClick?: (noteId: string, sourceSegments: string[]) => void
  onHighlightSegments?: (segmentIds: string[]) => void
}

/**
 * 支持定位功能的优化结构化笔记组件
 */
const PositionableStructuredNotes = memo<PositionableStructuredNotesProps>(({
  content,
  streamingContent,
  isAnalyzing,
  isExpanded,
  height,
  onToggleExpanded,
  onManualExpand,
  onManualCollapse,
  className = '',
  fullscreen = false,
  noteReferences = [],
  textSegments = [],
  onNoteClick,
  onHighlightSegments
}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const [hoveredNoteId, setHoveredNoteId] = useState<string | null>(null)
  
  // 智能滚动跟随
  const { shouldAutoScroll } = useSmartScrollFollow(contentRef, !!streamingContent)

  // 显示的内容（优先显示流式内容）
  const displayContent = streamingContent || content

  // 创建笔记ID到映射关系的索引
  const noteIdToMapping = useMemo(() => {
    const map = new Map<string, NoteReference>()
    for (const noteRef of noteReferences) {
      map.set(noteRef.noteId, noteRef)
    }
    return map
  }, [noteReferences])

  // 解析笔记内容，识别可定位的片段
  const parseNotesWithPositions = useCallback((noteContent: string) => {
    const lines = noteContent.split('\n')
    const elements: React.ReactNode[] = []
    let currentNoteId = ''
    let lineIndex = 0

    for (const line of lines) {
      const trimmedLine = line.trim()
      
      // 生成当前行的笔记ID
      if (trimmedLine.startsWith('#') || trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
        currentNoteId = `note_${lineIndex}`
      }

      // 查找对应的映射关系
      const mapping = Array.from(noteIdToMapping.values()).find(ref => 
        ref.noteContent.includes(trimmedLine) || 
        trimmedLine.includes(ref.noteContent.substring(0, 30))
      )

      const isPositionable = !!mapping
      const isHovered = hoveredNoteId === currentNoteId

      let lineClassName = 'block transition-all duration-200 '
      
      if (isPositionable) {
        lineClassName += 'cursor-pointer hover:bg-blue-50 border-l-2 border-transparent hover:border-blue-400 pl-2 -ml-2 '
      }
      
      if (isHovered) {
        lineClassName += 'bg-blue-100 border-blue-500 '
      }

      elements.push(
        <span
          key={lineIndex}
          className={lineClassName}
          onClick={() => isPositionable && handleNoteClick(currentNoteId, mapping)}
          onMouseEnter={() => isPositionable && handleNoteHover(currentNoteId, mapping)}
          onMouseLeave={() => handleNoteHover(null)}
          data-note-id={currentNoteId}
        >
          {line}
          
          {/* 定位指示器 */}
          {isPositionable && (
            <span className="inline-flex items-center ml-2 text-blue-500 opacity-60 hover:opacity-100">
              <Target className="w-3 h-3" />
              <span className="text-xs ml-1">
                {mapping?.sourceSegments.length || 0}
              </span>
            </span>
          )}
          
          {/* 悬停提示 */}
          {isHovered && mapping && (
            <div className="absolute z-50 left-full ml-2 top-0 p-2 bg-gray-800 text-white text-xs rounded shadow-lg max-w-xs">
              <div className="font-semibold mb-1">定位信息:</div>
              <div>关联片段: {mapping.sourceSegments.length}</div>
              <div>置信度: {Math.round(mapping.confidence * 100)}%</div>
              <div>类型: {mapping.mappingType}</div>
            </div>
          )}
        </span>
      )

      lineIndex++
    }

    return elements
  }, [noteIdToMapping, hoveredNoteId])

  // 处理笔记点击
  const handleNoteClick = useCallback((noteId: string, mapping?: NoteReference) => {
    if (!mapping) return

    // 高亮相关的原文片段
    onHighlightSegments?.(mapping.sourceSegments)
    
    // 触发定位回调
    onNoteClick?.(noteId, mapping.sourceSegments)
    
    console.log('点击笔记:', noteId, '关联片段:', mapping.sourceSegments)
  }, [onNoteClick, onHighlightSegments])

  // 处理笔记悬停
  const handleNoteHover = useCallback((noteId: string | null, mapping?: NoteReference) => {
    setHoveredNoteId(noteId)
    
    if (noteId && mapping) {
      // 临时高亮相关片段
      onHighlightSegments?.(mapping.sourceSegments)
    } else {
      // 清除高亮
      onHighlightSegments?.([])
    }
  }, [onHighlightSegments])

  // 自动滚动到底部（流式内容时）
  useEffect(() => {
    if (shouldAutoScroll && contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight
    }
  }, [displayContent, shouldAutoScroll])

  // 渲染内容
  const renderContent = useCallback(() => {
    if (isAnalyzing && !displayContent) {
      return (
        <div className="h-full flex items-center justify-center">
          <ModernLoader variant="dots" size="lg" text="正在生成结构化笔记..." className="text-center" />
        </div>
      )
    }

    if (!displayContent) {
      return (
        <div className="h-full flex items-center justify-center text-gray-500">
          暂无结构化笔记
        </div>
      )
    }

    // 全屏模式下的内容渲染
    if (fullscreen) {
      return (
        <div className="h-full overflow-y-auto">
          <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
            {displayContent}
          </SafeMarkdown>
        </div>
      )
    }

    // 卡片模式下的内容渲染 - 带定位功能
    return (
      <div className="bg-gradient-to-br from-slate-50/50 to-blue-50/30 rounded-xl p-4 relative">
        <div className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
          {noteReferences.length > 0 ? (
            <div className="space-y-1">
              {parseNotesWithPositions(displayContent)}
            </div>
          ) : (
            <SafeMarkdown>{displayContent}</SafeMarkdown>
          )}
        </div>
        
        {/* 定位功能状态指示 */}
        {noteReferences.length > 0 && (
          <div className="absolute top-2 right-2 flex items-center space-x-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full">
            <MapPin className="w-3 h-3" />
            <span>{noteReferences.length} 个定位点</span>
          </div>
        )}
      </div>
    )
  }, [isAnalyzing, displayContent, fullscreen, noteReferences, parseNotesWithPositions])

  // 全屏模式渲染
  if (fullscreen) {
    return (
      <div className={`h-full ${className}`}>
        {renderContent()}
      </div>
    )
  }

  // 卡片模式渲染
  return (
    <div
      className={`absolute top-4 left-4 right-4 bg-white backdrop-blur-lg rounded-3xl border border-slate-200/50 shadow-2xl shadow-slate-200/40 transition-all duration-300 ease-in-out ${className}`}
      style={{
        height: isExpanded ? `${height}%` : '60px',
        zIndex: 60,
        minHeight: '60px',
        maxHeight: '90%'
      }}
    >
      {/* 卡片头部 */}
      <div className="px-5 py-4 flex items-center justify-between border-b border-slate-200/50">
        <div className="flex items-center space-x-3">
          <div className="p-1.5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <h3 className="font-semibold text-slate-800 text-sm">结构化笔记</h3>
          
          {/* 定位功能指示 */}
          {noteReferences.length > 0 && (
            <div className="flex items-center space-x-1 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-full">
              <MapPin className="w-3 h-3" />
              <span>{noteReferences.length}</span>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {streamingContent && (
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          )}
          
          <button
            onClick={isExpanded ? onManualCollapse : onManualExpand}
            className="p-1.5 hover:bg-slate-100 rounded-lg transition-colors"
          >
            {isExpanded ? (
              <ChevronUp className="w-4 h-4 text-slate-600" />
            ) : (
              <ChevronDown className="w-4 h-4 text-slate-600" />
            )}
          </button>
        </div>
      </div>

      {/* 内容区域 */}
      <div
        ref={contentRef}
        className="px-5 py-4 overflow-y-auto"
        style={{
          height: isExpanded ? 'calc(100% - 73px)' : '0px',
          opacity: isExpanded ? 1 : 0
        }}
      >
        {renderContent()}
      </div>
    </div>
  )
})

PositionableStructuredNotes.displayName = 'PositionableStructuredNotes'

export default PositionableStructuredNotes
