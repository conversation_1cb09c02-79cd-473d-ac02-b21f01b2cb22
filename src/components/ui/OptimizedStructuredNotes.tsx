'use client'

import React, { memo, useMemo, useCallback, useRef, useEffect, useState } from 'react'
import { Sparkles, ChevronUp, ChevronDown, Save, Check, X } from 'lucide-react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'
import ModernLoader from '@/components/ui/ModernLoader'
import { useSmartScrollFollow } from '@/hooks/useSmartScrollFollow'
import { useAppStore, useActiveTab, useActiveChatMessages } from '@/lib/store'

interface OptimizedStructuredNotesProps {
  content: string
  streamingContent?: string
  isAnalyzing?: boolean
  isExpanded: boolean
  height: number
  onToggleExpanded: () => void
  onManualExpand: () => void
  onManualCollapse: () => void
  className?: string
  fullscreen?: boolean // 新增：全屏模式，无边框无卡片样式
}

// 使用memo优化渲染性能
const OptimizedStructuredNotes = memo<OptimizedStructuredNotesProps>(({
  content,
  streamingContent,
  isAnalyzing,
  isExpanded,
  height,
  onToggleExpanded,
  onManualExpand,
  onManualCollapse,
  className = '',
  fullscreen = false
}) => {
  const dragHandleRef = useRef<HTMLDivElement>(null)
  const { saveNote, mode } = useAppStore()
  const activeTab = useActiveTab()
  const chatMessages = useActiveChatMessages()

  // 保存状态
  const [isSaving, setIsSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'success' | 'error'>('idle')
  
  // 智能滚动跟随
  const {
    containerRef: scrollContainerRef,
    scrollState,
    forceScrollToBottom,
    scrollToBottom
  } = useSmartScrollFollow({
    enabled: isExpanded && (!!streamingContent || isAnalyzing),
    threshold: 50,
    smoothScroll: true,
    debounceMs: 100
  })

  // 使用useMemo优化内容计算
  const displayContent = useMemo(() => {
    return streamingContent || content || ''
  }, [streamingContent, content])

  // 使用useMemo优化状态计算
  const noteStatus = useMemo(() => {
    if (isAnalyzing) return 'analyzing'
    if (streamingContent) return 'streaming'
    if (content) return 'completed'
    return 'empty'
  }, [isAnalyzing, streamingContent, content])

  // 优化的切换处理函数
  const handleToggle = useCallback(() => {
    if (isExpanded) {
      onManualCollapse()
    } else {
      onManualExpand()
    }
  }, [isExpanded, onManualExpand, onManualCollapse])

  // 监听内容变化，触发滚动跟随
  useEffect(() => {
    if (streamingContent && isExpanded && scrollState.shouldAutoFollow) {
      // 使用防抖延迟滚动，避免过于频繁的滚动
      const timeoutId = setTimeout(() => {
        scrollToBottom()
      }, 100)

      return () => clearTimeout(timeoutId)
    }
  }, [streamingContent, isExpanded, scrollState.shouldAutoFollow, scrollToBottom])

  // 保存状态重置
  useEffect(() => {
    if (saveStatus !== 'idle') {
      const timer = setTimeout(() => {
        setSaveStatus('idle')
      }, 3000)
      return () => clearTimeout(timer)
    }
  }, [saveStatus])

  // 保存笔记功能
  const handleSaveNote = useCallback(async () => {
    if (!activeTab || isSaving) return

    setIsSaving(true)
    setSaveStatus('idle')

    try {
      // 首先整合对话内容
      const integrateResponse = await fetch('/api/notes/integrate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          originalContent: activeTab.originalContent,
          structuredNotes: displayContent,
          chatMessages: chatMessages
        })
      })

      if (!integrateResponse.ok) {
        throw new Error('整合对话内容失败')
      }

      const { integratedNotes } = await integrateResponse.json()

      // 保存整合后的笔记
      const savedNote = await saveNote({
        title: activeTab.title,
        originalContent: activeTab.originalContent,
        structuredNotes: displayContent,
        integratedNotes,
        sourceType: activeTab.sourceType,
        sourceData: activeTab.sourceData
      })

      if (savedNote) {
        setSaveStatus('success')
      } else {
        setSaveStatus('error')
      }
    } catch (error) {
      console.error('保存笔记失败:', error)
      setSaveStatus('error')
    } finally {
      setIsSaving(false)
    }
  }, [activeTab, chatMessages, displayContent, saveNote, isSaving])

  // 判断是否可以保存
  const canSave = useMemo(() => {
    return activeTab && displayContent && !isAnalyzing && !streamingContent
  }, [activeTab, displayContent, isAnalyzing, streamingContent])

  // 渲染状态指示器 - 根据用户要求移除状态标识
  const renderStatusIndicator = useCallback(() => {
    // 移除所有状态指示器
    return null
  }, [])

  // 渲染内容区域
  const renderContent = useCallback(() => {
    if (noteStatus === 'empty') {
      return (
        <div className="py-8 text-center">
          <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-3">
            <Sparkles className="w-6 h-6 text-gray-400" />
          </div>
          <p className="text-gray-500 text-sm">暂无结构化笔记</p>
        </div>
      )
    }

    if (noteStatus === 'analyzing') {
      return (
        <div className="py-8">
          <ModernLoader variant="dots" size="md" text="正在生成结构化笔记..." className="text-center" />
        </div>
      )
    }

    // 全屏模式下的内容渲染 - 无装饰背景
    if (fullscreen) {
      return (
        <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
          {displayContent}
        </SafeMarkdown>
      )
    }

    // 卡片模式下的内容渲染 - 带装饰背景
    return (
      <div className="bg-gradient-to-br from-slate-50/50 to-blue-50/30 rounded-xl p-4">
        <SafeMarkdown className="prose prose-sm max-w-none prose-headings:text-slate-800 prose-headings:font-semibold prose-p:text-slate-700 prose-p:leading-relaxed prose-ul:text-slate-700 prose-ol:text-slate-700 prose-li:my-1.5 prose-strong:text-slate-900 prose-code:text-blue-600 prose-code:bg-blue-50/80 prose-code:px-2 prose-code:py-0.5 prose-code:rounded-md prose-blockquote:border-l-4 prose-blockquote:border-blue-300 prose-blockquote:bg-blue-50/30 prose-blockquote:pl-4 prose-blockquote:py-2 prose-blockquote:rounded-r-lg">
          {displayContent}
        </SafeMarkdown>

        {/* 流式生成时的滚动指示器 */}
        {noteStatus === 'streaming' && !scrollState.isAtBottom && (
          <div className="fixed bottom-4 right-4 z-50">
            <button
              onClick={forceScrollToBottom}
              className="bg-blue-500 text-white p-2 rounded-full shadow-lg hover:bg-blue-600 transition-colors"
              title="滚动到底部"
            >
              <ChevronDown className="w-4 h-4" />
            </button>
          </div>
        )}
      </div>
    )
  }, [noteStatus, displayContent, scrollState.isAtBottom, forceScrollToBottom, fullscreen])

  // 全屏模式渲染
  if (fullscreen) {
    return (
      <div className={`h-full w-full ${className}`}>
        <div
          ref={scrollContainerRef}
          className="h-full overflow-y-auto"
        >
          {renderContent()}
        </div>
      </div>
    )
  }

  // 卡片模式渲染
  return (
    <div
      className={`absolute top-4 left-4 right-4 bg-white backdrop-blur-lg rounded-3xl border border-slate-200/50 shadow-2xl shadow-slate-200/40 transition-all duration-300 ease-in-out ${className}`}
      style={{
        height: isExpanded ? `${height}%` : '60px',
        zIndex: 60,
        minHeight: '60px',
        maxHeight: '90%'
      }}
    >
      {/* 卡片头部 */}
      <div className="px-5 py-4 flex items-center justify-between border-b border-slate-200/50">
        <div className="flex items-center space-x-3">
          <div className="p-1.5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
            <Sparkles className="w-4 h-4 text-white" />
          </div>
          <h3 className="font-semibold text-slate-800 text-sm">结构化笔记</h3>
          {renderStatusIndicator()}
        </div>

        <div className="flex items-center space-x-2">
          {/* 保存按钮 */}
          {canSave && (
            <button
              onClick={handleSaveNote}
              disabled={isSaving}
              className={`p-1.5 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20 ${
                saveStatus === 'success'
                  ? 'bg-green-100 text-green-600 hover:bg-green-200'
                  : saveStatus === 'error'
                  ? 'bg-red-100 text-red-600 hover:bg-red-200'
                  : 'hover:bg-slate-100 text-slate-600 hover:text-blue-600'
              } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
              title={
                saveStatus === 'success'
                  ? '保存成功'
                  : saveStatus === 'error'
                  ? '保存失败，点击重试'
                  : '保存到知识库'
              }
            >
              {isSaving ? (
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              ) : saveStatus === 'success' ? (
                <Check className="w-4 h-4" />
              ) : saveStatus === 'error' ? (
                <X className="w-4 h-4" />
              ) : (
                <Save className="w-4 h-4" />
              )}
            </button>
          )}

          {/* 折叠展开按钮 */}
          <button
            onClick={handleToggle}
            className="p-1.5 rounded-lg hover:bg-slate-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            title={isExpanded ? "折叠" : "展开"}
          >
            {isExpanded ? (
              <ChevronUp className="w-4 h-4 text-slate-600" />
            ) : (
              <ChevronDown className="w-4 h-4 text-slate-600" />
            )}
          </button>
        </div>
      </div>

      {/* 卡片内容 - 可滚动，支持折叠展开 */}
      {isExpanded && (
        <div
          ref={scrollContainerRef}
          className="flex-1 overflow-y-auto px-4 py-4"
          style={{ height: 'calc(100% - 80px)' }}
        >
          {renderContent()}
        </div>
      )}

      {/* 拖拽手柄 - 更圆润的设计 */}
      <div
        ref={dragHandleRef}
        className="absolute bottom-0 left-0 right-0 h-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-b-3xl cursor-ns-resize hover:from-blue-500/40 hover:to-purple-500/40 transition-all duration-200"
        title="拖拽调整高度"
      >
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-1.5 bg-slate-400/80 rounded-full"></div>
      </div>
    </div>
  )
})

OptimizedStructuredNotes.displayName = 'OptimizedStructuredNotes'

export default OptimizedStructuredNotes
